package com.dcai.aixg.domain.aiagent.generate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.api.GenerateAPI;
import com.dcai.aixg.domain.aiagent.flow.FlowService;
import com.dcai.aixg.domain.aiagent.model.Model;
import com.dcai.aixg.domain.aiagent.model.ModelRpt;
import com.dcai.aixg.dto.GenerateDTO;
import com.dcai.aixg.pro.LaunchGeneratePO;
import com.ejuetc.commons.base.response.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.dcai.aixg.domain.aiagent.generate.Generate.newGenerate;
import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.ThreadUtils.asyncExec;

/**
 * 生成API实现
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Transactional
public class GenerateService implements GenerateAPI {

    private final GenerateRpt generateRpt;
    private final GenerateConfigRpt generateConfigRpt;
    private final ModelRpt modelRpt;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ApiResponse<GenerateDTO> launch(LaunchGeneratePO po) {
        Generate generate = getBean(GenerateService.class).doCreate(po);
        Long generateId = generate.getId();
        switch (po.getRunMode()) {
            case SYNC:
                generate = doExec(generateId);
                break;
            case ASYNC:
                asyncExec(() -> getBean(GenerateService.class).doExec(generateId));
                break;
            case STREAM:
                throw new RuntimeException("流式模式暂不支持");
        }
        return succ(convert2DTO(generate, new GenerateDTO()));
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ApiResponse<Long> launch4test(String modeName, String resultType, String promptContent) {
        PromptContent prompt = new PromptContent(promptContent);
        String systemPrompt = prompt.getSystemPrompt();
        String userPrompt = prompt.getUserPrompt();
        Generate generate = getBean(GenerateService.class).doCreate(modeName, systemPrompt, userPrompt, resultType);
        asyncExec(() -> getBean(GenerateService.class).doExec(generate.getId()));
        return succ(generate.getId());
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    protected Generate doCreate(String modeName, String systemPrompt, String userPrompt, String resultType) {
        Model<?> model = modelRpt.findByName(modeName);
        return new Generate4LlmGen(model, systemPrompt, userPrompt, resultType).save();
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    protected Generate doCreate(LaunchGeneratePO po) {
        GenerateConfig config = generateConfigRpt.findByCode(po.getConfigCode())
                .orElseThrow(() -> new RuntimeException("生成器配置不存在: " + po.getConfigCode()));
        return newGenerate(config, new JSONObject(po.getRequest())).save();
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Generate doExec(Long generateId) {
        Generate generate = generateRpt.getReferenceById(generateId);
        generate.exec();
        return generate;
    }

    @Override
    public ApiResponse<?> receiveAsyncResponse(Long generateId, GenerateDTO.Status status, String result) {
        Generate generate = getBean(GenerateService.class).doReceiveAsyncResponse(generateId, status, result);
        Long flowId = generate.getFlowId();
        if (flowId != null) getBean(FlowService.class).execFlow(flowId);
        return succ();
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    protected Generate doReceiveAsyncResponse(Long generateId, GenerateDTO.Status status, String result) {
        Generate generate = generateRpt.getReferenceById(generateId);
        generate.receiveAsyncResponse(status, result);
        return generate;
    }

    @Override
    public ResponseEntity<String> queryResponse(Long id) {
        Generate generate = generateRpt.getReferenceById(id);
        String content;
        MediaType mediaType;

        if (!generate.isCompleted()) {
            content = "生成中...";
            mediaType = MediaType.TEXT_PLAIN;
        } else if (!generate.isSuccess()) {
            content = "生成失败:" + generate.getErrorMessage();
            mediaType = MediaType.TEXT_PLAIN;
        } else {
            content = generate.getResponse();
            // 判断响应内容类型，如果是JSON格式则设置为application/json
            if (content != null && content.trim().startsWith("{") && content.trim().endsWith("}")) {
                mediaType = MediaType.APPLICATION_JSON;
            } else {
                mediaType = MediaType.TEXT_PLAIN;
            }
        }

        return ResponseEntity.ok()
                .contentType(mediaType)
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline")
                .body(content);
    }

    @GetMapping("/web/generate/query")
    public JSONObject webQuery(@RequestParam Long id) {
        Generate generate = generateRpt.getReferenceById(id);
        JSONObject obj = new JSONObject();
        if (!generate.isCompleted()) {
            obj.put("code", 100);
            obj.put("message", "生成中");
        } else if (generate.isSuccess()) {
            obj.put("code", 200);
            obj.put("message", "成功");
            obj.put("data", JSON.parseObject(generate.getResponse()));
        } else {
            obj.put("code", 500);
            obj.put("message", "生成失败:" + generate.getErrorMessage());
        }
        return obj;
    }
}
