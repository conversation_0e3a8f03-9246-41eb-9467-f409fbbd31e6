package com.dcai.aixg.domain.aiagent.model;

import com.ejuetc.commons.base.entity.SuperEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;
import org.springframework.ai.chat.model.ChatModel;

@Entity
@Table(name = "tb_ai_model")
@Comment("大语言模型")
@Where(clause = "logic_delete = 0")
@NoArgsConstructor
@Accessors(chain = true)
@Getter
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", columnDefinition = "ENUM('OPENAI','OLLAMA','DEEPSEEK') COMMENT '模型类型'")
public abstract class Model<Subtype extends Model<?>> extends SuperEntity<Subtype> {

    @Id
    @GeneratedValue(generator = "model_id")
    @SequenceGenerator(name = "model_id", sequenceName = "seq_model_id")
    protected Long id;

    @Column(name = "name", columnDefinition = "varchar(50) COMMENT '模型名称'", nullable = false)
    protected String name;



    @Column(name = "base_url", columnDefinition = "varchar(255) COMMENT '请求地址'")
    protected String baseUrl;

    @Column(name = "api_key", columnDefinition = "varchar(255) COMMENT 'api密钥'")
    protected String apiKey;

    @Column(name = "model", columnDefinition = "varchar(63) COMMENT '模型名称'")
    protected String model;

    @Column(name = "temperature", columnDefinition = "float COMMENT '温度'")
    protected Float temperature;

    @Column(name = "max_tokens", columnDefinition = "int COMMENT '最大生成长度'")
    protected Integer maxTokens;

    @Transient
    @Getter(lazy = true)
    @Accessors(fluent = true)
    private final ChatModel chatModel = buildChatClient();

    protected abstract ChatModel buildChatClient();

    @Override
    public Long getId() {
        return id;
    }

}
