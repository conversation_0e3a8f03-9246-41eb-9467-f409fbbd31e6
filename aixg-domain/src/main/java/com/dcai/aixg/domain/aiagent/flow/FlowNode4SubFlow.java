package com.dcai.aixg.domain.aiagent.flow;

import com.dcai.aixg.domain.aiagent.commons.ProcessBase;
import com.dcai.aixg.pro.LaunchFlowPO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.dcai.aixg.dto.FlowDTO.SrcType.SUBFLOW;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@Slf4j
@Getter
@Entity
@NoArgsConstructor
@DiscriminatorValue("SUBFLOW")
@SubtypeCode(parent = FlowNode.class, code = "SUBFLOW", name = "生成器")
public class FlowNode4SubFlow extends FlowNode {

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "flow_config_id", columnDefinition = "bigint(20) COMMENT '流程配置ID'")
    private FlowConfig flowConfig;

    @Override
    public ProcessBase<?> newProcess(Flow flow) {
        LaunchFlowPO po = new LaunchFlowPO()
                .setConfigCode(flowConfig.getCode())
                .setRequest(flow.getRequest())
                .setSrcId(flow.getId())
                .setSrcType(SUBFLOW)
                .setRunMode(LaunchFlowPO.RunMode.SYNC);
        return new Flow(flowConfig, po);
    }
}
