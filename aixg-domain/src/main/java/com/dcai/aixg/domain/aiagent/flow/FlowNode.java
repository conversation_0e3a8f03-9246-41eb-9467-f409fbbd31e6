package com.dcai.aixg.domain.aiagent.flow;

import com.dcai.aixg.domain.aiagent.commons.ProcessBase;
import com.dcai.aixg.dto.FlowNodeDTO;
import com.ejuetc.commons.base.clazz.ClassUtils;
import com.ejuetc.commons.base.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Getter
@Entity
@NoArgsConstructor
@Comment("工作流节点")
@Table(name = "tb_ai_flow_node")
@Where(clause = "logic_delete = 0")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", columnDefinition = "enum('GENERATE','SUBFLOW') COMMENT '类型'")
public abstract class FlowNode extends BaseEntity<FlowNode> {
    @Id
    @Comment("节点ID")
    @GeneratedValue(generator = "flow_node_id")
    @SequenceGenerator(name = "flow_node_id", sequenceName = "seq_flow_node_id")
    private Long id;

    @Column(name = "name", columnDefinition = "varchar(200) COMMENT '节点名称'")
    private String name;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_id", columnDefinition = "bigint(20) COMMENT '父节点ID'")
    private FlowNode parent;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, insertable = false, updatable = false)
    private FlowNodeDTO.Type type;


    @OrderColumn(name = "sort", columnDefinition = "int COMMENT '步骤顺序'")
    @OneToMany(mappedBy = "parent", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<FlowNode> children = new ArrayList<>();

    public FlowNode nextNode(Flow flow) {
        return children != null && !children.isEmpty() ? children.get(0) : null;
    }

    public abstract ProcessBase<?> newProcess(Flow flow);

    public FlowNodeDTO.Type getType() {
        if (type == null)
            type = FlowNodeDTO.Type.valueOf(ClassUtils.getClassAnnotation(this.getClass(), DiscriminatorValue.class).value());
        return type;
    }

}
