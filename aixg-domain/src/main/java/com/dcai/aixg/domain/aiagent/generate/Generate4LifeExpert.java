package com.dcai.aixg.domain.aiagent.generate;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.api.WriteAPI;
import com.dcai.aixg.domain.aiagent.flow.Flow;
import com.dcai.aixg.domain.aiagent.flow.FlowNode;
import com.dcai.aixg.dto.GenerateDTO;
import com.dcai.aixg.dto.task.WriteCreateDTO;
import com.dcai.aixg.pro.task.WriteCreatePO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.response.ApiResponse;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@Entity
@NoArgsConstructor
@DiscriminatorValue("LIFE_EXPERT")
@SubtypeCode(parent = Generate.class, code = "LIFE_EXPERT", name = "房产生活专家")
public class Generate4LifeExpert extends Generate {

    protected Generate4LifeExpert(GenerateConfig config, JSONObject request, Flow flow, FlowNode flowNode) {
        super(config, request, flow, flowNode);
    }

    @Override
    protected GenerateDTO.Status doExec() throws Exception {
        log.info("调用Cric获取数据");
        ObjectMapper mapper = new ObjectMapper();
        WriteCreatePO po = mapper.readValue(getRequest().toJSONString(), WriteCreatePO.class);
        ApiResponse<WriteCreateDTO> result = getBean(WriteAPI.class).doWriteByTaskId(getFlow().getSrcId(), this.getFlow().getId(), po);
        //setResponse(JSONObject.toJSONString(result.getData()));
        setProcessInfo2(result.getData().getWriteId());
        setProcessInfo3(result.getData().getArticleId());
        return GenerateDTO.Status.GENERATING;
    }

}
