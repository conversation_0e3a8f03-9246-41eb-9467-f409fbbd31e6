package com.dcai.aixg.domain.search;

import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import com.dcai.aixg.domain.broker.Broker;
import com.dcai.aixg.pro.search.ChatCreatePO;
import com.ejuetc.commons.base.entity.BaseEntity;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Entity
@NoArgsConstructor
@Comment("搜索")
@Table(name = "tb_search")
@Where(clause = "logic_delete = 0")
public class Search extends BaseEntity<Search> {

    @Id
    @Comment("搜索id")
    @GeneratedValue(generator = "search_id")
    @SequenceGenerator(name = "search_id", sequenceName = "seq_search_id")
    private Long id;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "broker_id", columnDefinition = "bigint(20) COMMENT '经纪人ID'")
    private Broker broker;

    @Column(name = "chatId", columnDefinition = "varchar(255) COMMENT '搜索ID'")
    private String chatId;

    @Column(name = "status", columnDefinition = "bigint(20) default 0 COMMENT '状态，0：未发起搜索，1:已完成'")
    private Integer status = 0;

    @Column(name = "query", columnDefinition = "varchar(255) COMMENT '搜索内容'")
    private String query;

    @Column(name = "vote_status", columnDefinition = "bigint(20) default 0 COMMENT '评价状态，1：好评，-1:差评'")
    private Integer voteStatus = 0;
    
    public Search(Broker broker, ChatCreatePO po) {
    	this.broker = broker;
    	this.query = po.getQuery();
    }
    
    public void setChatId(String chatId) {
    	this.chatId = chatId;
    }
    
    public void setLogicDelete() {
    	this.logicDelete = true;
    }
    
    public void setStatus(int status) {
    	this.status = status;
    }
    
    public void setVoteStatus(int voteStatus) {
    	this.voteStatus = voteStatus;
    }

}
