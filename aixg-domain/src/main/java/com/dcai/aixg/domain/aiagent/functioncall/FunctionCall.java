package com.dcai.aixg.domain.aiagent.functioncall;

import com.dcai.aixg.domain.aiagent.commons.ProcessBase;
import com.dcai.aixg.domain.aiagent.generate.Generate;
import com.dcai.aixg.domain.aiagent.generate.Generate4LlmGen;
import com.dcai.aixg.domain.commons.ThreadLocalStack;
import com.dcai.aixg.dto.ProcessDTO;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.response.ResponseStatus;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;
import org.springframework.ai.tool.annotation.Tool;

import java.lang.reflect.Method;
import java.time.LocalDateTime;

import static com.alibaba.fastjson.JSON.toJSONString;
import static com.dcai.aixg.domain.aiagent.generate.Generate.getRunningGenerate;
import static com.ejuetc.commons.base.response.ApiResponse.apiResponse;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.StringUtils.notBlank;
import static java.time.Duration.between;

/**
 * 生成回调函数实体
 */
@Getter
@Entity
@Comment("AI函数回调")
@Table(name = "tb_ai_function_call")
@Where(clause = "logic_delete = 0")
@NoArgsConstructor
public class FunctionCall extends ProcessBase<FunctionCall> {

    @Id
    @Comment("回调函数ID")
    @GeneratedValue(generator = "generate_step_id")
    @SequenceGenerator(name = "generate_step_id", sequenceName = "seq_function_call_id")
    private Long id;

    @Column(name = "tool_name", columnDefinition = "varchar(511) COMMENT '工具名'")
    private String toolName;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ResponseStatus status;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_generate_id", columnDefinition = "bigint(20) COMMENT '发起回调的生成任务'")
    private Generate parentGenerate;

    @Setter
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "child_generate_id", columnDefinition = "bigint(20) COMMENT '回调发起的生成任务'")
    private Generate childGenerate;

    @Column(name = "sort", columnDefinition = "int default 0 COMMENT '回调函数顺序'", nullable = false, insertable = false, updatable = false)
    private Integer sort;

    @Column(name = "request", columnDefinition = "json COMMENT '请求内容'")
    private String request;

    private final static ThreadLocalStack<Long> runningIdStack = new ThreadLocalStack<>();

    /**
     * 构造函数
     */
    public FunctionCall(ProceedingJoinPoint joinPoint) {
        this.toolName = getToolName(joinPoint);
        this.status = ResponseStatus.WAIT;
        this.request = getArgs(joinPoint);
        this.parentGenerate = getRunningGenerate();
        if (parentGenerate == null) throw new CodingException("未找到父生成器,FunctionCalling (%s) 无法进行!", toolName);
        this.sort = this.parentGenerate.subType(Generate4LlmGen.class).addStep(this);
        log.info("创建回调函数(sort:{} id:{} toolName:{} request:{})", sort, id, toolName, request);
    }

    private static String getArgs(ProceedingJoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        return args == null || args.length == 0 ? "" : toJSONString(args.length == 1 ? args[0] : args);
    }

    private static String getToolName(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Tool toolAnnotation = method.getAnnotation(Tool.class);
        return notBlank(toolAnnotation.name())
                ? toolAnnotation.name()
                : method.getDeclaringClass().getSimpleName() + "." + method.getName();
    }

    public Object exec(ProceedingJoinPoint joinPoint) {
        try {
            log.info("执行回调函数(sort:{} id:{} toolName:{} request:{})", sort, id, toolName, request);
            runningIdStack.push(id);
            timerStart();
            Object result = joinPoint.proceed();
            if (result instanceof ApiResponse<?> resp) {
                this.status = resp.getStatus();
                this.setErrorMessage(resp.getMessage());
                this.setResponse(toJSONString(resp.getData()));
            } else {
                this.status = ResponseStatus.SUCC_DONE;
                this.setResponse(toJSONString(result));
            }
            return result;
        } catch (Throwable e) {
            log.error("回调函数(" + id + ")执行失败: ", e);
            this.status = ResponseStatus.UNKNOW;
            this.setErrorMessage(e.getMessage());
            return apiResponse(e);
        } finally {
            timerStop();
            Long popId = runningIdStack.pop();
            if (!id.equals(popId))
                log.error("堆栈弹出异常: 预期id({}),实际id({})", id, popId);
            log.info("回调函数执行完成(sort:{} id:{} toolName:{} response:{})", sort, id, toolName, getResponse());
        }
    }

    public static FunctionCall getRunningFunctionCall() {
        Long fcId = runningIdStack.peek();
        return fcId != null ? getBean(FunctionCallRpt.class).getReferenceById(fcId) : null;
    }

    @Override
    public boolean isSuccess() {
        return status == ResponseStatus.SUCC_DONE;
    }

    @Override
    public boolean isCompleted() {
        return status.getGroup() != null;
    }

    @Override
    public ProcessDTO.Type getProcessType() {
        return ProcessDTO.Type.FUNCTION_CALL;
    }

}
