package com.dcai.aixg.domain.aiagent.generate;

import com.ejuetc.commons.base.utils.IOUtils;
import lombok.Getter;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.HashMap;
import java.util.Map;

@Getter
public class PromptContent {

    private static final String BEGIN_SPLIT = "<----------------------------(";
    private static final String END_SPLIT = ")---------------------------->";
    public static final String SYSTEM_PROMPT_KEY = "system_prompt";
    public static final String USER_PROMPT_KEY = "user_prompt";
    public static final String PLACE_HOLDER = "${%s}";

    private final String promptContent;
    private String systemPrompt;
    private String userPrompt;

    public PromptContent(String promptContent) {
        this.promptContent = promptContent;
        splitPrompt();
    }

    private void splitPrompt() {
        Map<String, String> result = new HashMap<>();
        int startIdx = 0;
        while (true) {
            Prompt prompt = splitProm(promptContent, startIdx);
            result.put(prompt.key, prompt.content);
            if (prompt.finishIdx == promptContent.length()) break;
            startIdx = prompt.finishIdx;
        }
        systemPrompt = result.get(SYSTEM_PROMPT_KEY);
        userPrompt = result.get(USER_PROMPT_KEY);
        if (systemPrompt == null || userPrompt == null) throw new RuntimeException("提示词格式错误，未找到系统提示或用户提示");

        // 替换占位符
        for (String key : result.keySet()) {
            String placeHolder = PLACE_HOLDER.formatted(key);
            String replacement = result.get(key);
            systemPrompt = systemPrompt.replace(placeHolder, replacement);
            userPrompt = userPrompt.replace(placeHolder, replacement);
        }

    }

    private Prompt splitProm(String prompt, int startIdx) {
        int beginIdx = prompt.indexOf(BEGIN_SPLIT, startIdx);
        if (beginIdx == -1) throw new RuntimeException("提示词格式错误，未找到分隔符: " + BEGIN_SPLIT);
        int endIdx = prompt.indexOf(END_SPLIT, beginIdx + BEGIN_SPLIT.length());
        if (endIdx == -1) throw new RuntimeException("提示词格式错误，未找到分隔符: " + END_SPLIT);
        String key = prompt.substring(beginIdx + BEGIN_SPLIT.length(), endIdx);
        int finishIdx = prompt.indexOf(BEGIN_SPLIT, endIdx + END_SPLIT.length());
        if (finishIdx == -1) finishIdx = prompt.length();
        String content = prompt.substring(endIdx + END_SPLIT.length(), finishIdx).trim();
        return new Prompt(key, content, finishIdx);
    }

    record Prompt(String key, String content, int finishIdx) {
    }

    public static void main(String[] args) throws FileNotFoundException {
        String file = "/Users/<USER>/IdeaProjects/dcai/aixg/docs/prompt/1_政策解读专家/backup/prom.md";
        String fileContent = IOUtils.read(new FileInputStream(file));

        PromptContent promptContent = new PromptContent(fileContent);
        System.out.println(promptContent.getSystemPrompt());
        System.out.println(promptContent.getUserPrompt());
    }
}
