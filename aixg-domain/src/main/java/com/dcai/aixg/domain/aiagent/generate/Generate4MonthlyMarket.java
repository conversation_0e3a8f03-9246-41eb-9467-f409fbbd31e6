package com.dcai.aixg.domain.aiagent.generate;

import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.api.WriteAPI;
import com.dcai.aixg.domain.aiagent.flow.Flow;
import com.dcai.aixg.domain.aiagent.flow.FlowNode;
import com.dcai.aixg.dto.GenerateDTO;
import com.dcai.aixg.dto.task.WriteCreateDTO;
import com.dcai.aixg.pro.task.WriteCreatePO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.response.ApiResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

/**
 * 生成任务实体
 *
 * <AUTHOR>
 */
@Slf4j
@Getter
@Entity
@NoArgsConstructor
@DiscriminatorValue("MONTHLY_REPORT")
@SubtypeCode(parent = Generate.class, code = "MONTHLY_REPORT", name = "月度分析报告")
public class Generate4MonthlyMarket extends Generate {

    protected Generate4MonthlyMarket(GenerateConfig config, JSONObject request, Flow flow, FlowNode flowNode) {
        super(config, request, flow, flowNode);
    }

    @Override
    protected GenerateDTO.Status doExec() throws Exception {
        log.info("调用Cric获取数据");
        ObjectMapper mapper = new ObjectMapper();
        Long kerUserId = getRequest().getLong("userId");
        WriteCreatePO po = mapper.readValue(getRequest().toJSONString(), WriteCreatePO.class);
        ApiResponse<WriteCreateDTO> result = getBean(WriteAPI.class).doWrite(kerUserId, po);
        log.info("调用Cric获取数据 po = {}, result = {}", po, result);
        setProcessInfo1(JSONObject.toJSONString(po, true));
        setProcessInfo2(result.getData().getWriteId());
        setProcessInfo3(result.getData().getArticleId());
        receiveAsyncResponse(GenerateDTO.Status.SUCCESS, JSONObject.toJSONString(result.getData()));
        return GenerateDTO.Status.GENERATING;
    }
}
