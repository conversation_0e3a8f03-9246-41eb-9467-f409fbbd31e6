package com.dcai.aixg.domain.aiagent.flow;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.api.FlowAPI;
import com.dcai.aixg.domain.aiagent.commons.ProcessBase;
import com.dcai.aixg.domain.aiagent.generate.Generate;
import com.dcai.aixg.domain.aiagent.generate.GenerateService;
import com.dcai.aixg.dto.FlowDTO;
import com.dcai.aixg.dto.GenerateDTO;
import com.dcai.aixg.pro.LaunchFlowPO;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.response.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.ThreadUtils.asyncExec;

/**
 * 工作流服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
@RestController
public class FlowService implements FlowAPI {

    private final FlowRpt flowRpt;
    private final FlowConfigRpt flowConfigRpt;
    private final GenerateService generateService;

    /**
     * 创建并执行工作流
     */
    @Override
    public ApiResponse<FlowDTO> launch(LaunchFlowPO po) {
        log.info("创建工作流: {}", po);
        Flow flow = flowRpt.findBySrcTypeAndSrcId(po.getSrcType(), po.getSrcId())
                .orElseGet(() -> getBean(FlowService.class).createFlow(po));
        Long flowId = flow.getId();
        if (flow.isWait())
            switch (po.getRunMode()) {
                case SYNC:
                    getBean(FlowService.class).execFlow(flowId);
                    flow = flowRpt.getReferenceById(flowId);
                    break;
                case ASYNC:
                    asyncExec(() -> getBean(FlowService.class).execFlow(flowId));
                    break;
            }

        return succ(convert2DTO(flow, new FlowDTO().setLastGenerate(new GenerateDTO())));
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    protected Flow createFlow(LaunchFlowPO po) {
        FlowConfig config = flowConfigRpt.findByCode(po.getConfigCode()).orElseThrow(() -> new RuntimeException("工作流配置不存在: " + po.getConfigCode()));
        return new Flow(config, po).save();
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public ProcessBase<?> execFlow(Long flowId) {
        ProcessBase<?> execedProcess = null;
        while (true) {
            ProcessBase<?> nextProcess = getBean(FlowService.class).createNextProcess(flowId);
            if (nextProcess == null) break;
            if (nextProcess instanceof Generate generate) {
                execedProcess = generateService.doExec(generate.getId());
            } else if (nextProcess instanceof Flow subFlow) {
                execedProcess = execFlow(subFlow.getId());
            } else {
                throw new CodingException("意料之外的处理器类型:" + nextProcess);
            }
            if (!execedProcess.isSuccess()) break;
        }
        if (execedProcess != null && execedProcess.isCompleted())
            getBean(FlowService.class).notifySource(flowId);
        return execedProcess;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    protected void notifySource(Long flowId) {
        flowRpt.getReferenceById(flowId).notifySource();
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    protected ProcessBase<?> createNextProcess(Long flowId) {
        return flowRpt.getReferenceById(flowId).createNextProcess();
    }

    public ApiResponse<FlowDTO> query(Long id) {
        Flow flow = flowRpt.getReferenceById(id);
        return succ(convert2DTO(flow, new FlowDTO()));
    }

    @GetMapping("/web/flow/query")
    public JSONObject webQuery(@RequestParam Long id) {
        Flow flow = flowRpt.getReferenceById(id);
        JSONObject obj = new JSONObject();
        obj.put("code", 200);
        obj.put("message", "成功");
        obj.put("data", JSON.parseObject(flow.getResponse()));
        return obj;
    }


}
