package com.dcai.aixg.domain.aiagent.flow;

import com.dcai.aixg.domain.aiagent.generate.GenerateConfig;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.usertype.MapContentUT;
import com.ejuetc.commons.base.valueobj.MapContent;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import java.util.ArrayList;
import java.util.List;

/**
 * 工作流配置实体
 * <p>
 * 定义一个工作流包含哪些生成任务，以及它们的执行顺序
 * 通过数据库直接配置，不提供管理性代码
 *
 * <AUTHOR>
 */
@Getter
@Entity
@NoArgsConstructor
@Comment("工作流配置")
@Table(name = "tb_ai_flow_config")
@Where(clause = "logic_delete = 0")
public class FlowConfig extends BaseEntity<FlowConfig> {

    @Id
    @Comment("配置ID")
    @GeneratedValue(generator = "flow_config_id")
    @SequenceGenerator(name = "flow_config_id", sequenceName = "seq_flow_config_id")
    private Long id;

    @Column(name = "code", columnDefinition = "varchar(100) COMMENT '工作流代码'", nullable = false, unique = true)
    private String code;

    @Column(name = "title", columnDefinition = "varchar(200) COMMENT '工作流标题'", nullable = false)
    private String title;

    @Column(name = "description", columnDefinition = "text COMMENT '工作流描述'")
    private String description;

    @Type(MapContentUT.class)
    @Column(name = "properties", columnDefinition = "varchar(511) COMMENT '流程属性配置'")
    private MapContent properties = new MapContent();

//    @ManyToMany(fetch = FetchType.LAZY)
//    @JoinTable(
//            name = "tb_ai_flow_config_generate_config",
//            joinColumns = @JoinColumn(name = "flow_config_id"),
//            inverseJoinColumns = @JoinColumn(name = "generate_config_id")
//    )
//    @OrderColumn(name = "sort", columnDefinition = "int COMMENT '步骤顺序'")
//    private List<GenerateConfig> generateConfigs = new ArrayList<>();


    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "root_node_id", columnDefinition = "bigint(20) COMMENT '流程根节点ID'")
    private FlowNode rootNode;

}
