package com.dcai.aixg.domain.aiagent.generate;

import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.domain.aiagent.flow.Flow;
import com.dcai.aixg.domain.aiagent.flow.FlowNode;
import com.dcai.aixg.domain.aiagent.functioncall.FunctionCall;
import com.dcai.aixg.domain.aiagent.model.Model;
import com.dcai.aixg.dto.GenerateDTO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;

import java.util.ArrayList;
import java.util.List;

import static com.dcai.aixg.dto.GenerateDTO.Status.SUCCESS;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.StringUtils.isBlank;

/**
 * 生成任务实体
 *
 * <AUTHOR>
 */
@Slf4j
@Getter
@Entity
@NoArgsConstructor
@DiscriminatorValue("LLM_GEN")
@SubtypeCode(parent = Generate.class, code = "LLM_GEN", name = "大模型生成")
public class Generate4LlmGen extends Generate {

    public static final String RESULT_TYPE_JSON = "JSON";
    public static final String MD_CODE_JSON = "```json";
    public static final String MD_CODE_END = "```";


    @OrderColumn(name = "sort", columnDefinition = "int COMMENT '排序'")
    @OneToMany(mappedBy = "parentGenerate", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private final List<FunctionCall> steps = new ArrayList<>();

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "model_id", columnDefinition = "bigint(20) COMMENT '模型ID'")
    private Model<?> model;

    @Column(name = "result_type", columnDefinition = "varchar(255) COMMENT '结果类型'")
    private String resultType;

    protected Generate4LlmGen(GenerateConfig config, JSONObject request, Flow flow, FlowNode flowNode) {
        super(config, request, flow, flowNode);
        this.model = config.getModel();
        this.resultType = config.getResultType();
    }

    public Generate4LlmGen(Model<?> model, String sysProm, String userProm, String resultType) {
        super();
        this.model = model;
        this.setSysProm(sysProm);
        this.setUserProm(userProm);
        this.resultType = resultType;
    }

    @Override
    protected GenerateDTO.Status doExec() throws Exception {
        String sysProm = getSysProm();
        if (isBlank(sysProm)) {
            sysProm = buildSystemPrompt();
            setSysProm(sysProm);
        }
        String userProm = getUserProm();
        if (isBlank(userProm)) {
            userProm = buildUserPrompt();
            setUserProm(userProm);
        }

        ChatClient.CallResponseSpec call = ChatClient
                .builder(model.chatModel())
//                .defaultTools(getBean(FunctionCallService.class))
                .build()
                .prompt()
                .system(sysProm)
                .user(userProm)
                .call();

        log.debug("向大模型[{}]发出生成请求...", model.getName());
        String response;
        if (isBlank(resultType)) {
            response = call.content();
        } else if (RESULT_TYPE_JSON.equals(resultType)) {
            response = call.content();
            response = clearMdJsonFlag(response);
        } else {
            Object result = call.entity(Class.forName(resultType));
            response = getBean(ObjectMapper.class).writeValueAsString(result);
        }
        setResponse(response);

        log.debug("AI生成完成，任务ID: {}, 响应长度: {}", getId(), getResponseLength());
        return SUCCESS;
    }


    public static String clearMdJsonFlag(String string) {
        if (isBlank(string)) return string;
        int beginIdx = string.indexOf(MD_CODE_JSON);
        if (beginIdx == -1)
            beginIdx = string.indexOf(MD_CODE_JSON.toUpperCase());
        if (beginIdx == -1) return string;

        string = string.substring(beginIdx + MD_CODE_JSON.length());
        int endIdx = string.lastIndexOf(MD_CODE_END);
        if (endIdx == -1) return string;

        return string.substring(0, endIdx);
    }

    protected String buildSystemPrompt() {
        return getConfig().getSystemTemplate().parse(this);
    }

    protected String buildUserPrompt() {
        return getConfig().getUserTemplate().parse(this);
    }

    public int addStep(FunctionCall step) {
        if (!steps.contains(step)) steps.add(step);
        return steps.indexOf(step);
    }

    public String getSysProm() {
        return getProcessInfo1();
    }

    public void setSysProm(String sysProm) {
        setProcessInfo1(sysProm);
    }

    public String getUserProm() {
        return getProcessInfo2();
    }

    public void setUserProm(String userProm) {
        setProcessInfo2(userProm);
    }
}
