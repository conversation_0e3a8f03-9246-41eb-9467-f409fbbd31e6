package com.dcai.aixg.domain.aiagent.generate;

import com.dcai.aixg.domain.aiagent.flow.Flow;
import com.dcai.aixg.domain.aiagent.flow.FlowNode;
import com.dcai.aixg.domain.aiagent.model.Model;
import com.dcai.aixg.dto.GenerateDTO;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.usertype.FMTemplateUT;
import com.ejuetc.commons.base.usertype.MapContentUT;
import com.ejuetc.commons.base.valueobj.FMTemplate;
import com.ejuetc.commons.base.valueobj.MapContent;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

/**
 * 生成配置实体
 *
 * <AUTHOR>
 */
@Getter
@Entity
@NoArgsConstructor
@Comment("生成配置")
@Table(name = "tb_ai_generate_config")
@Where(clause = "logic_delete = 0")
public class GenerateConfig extends BaseEntity<GenerateConfig> {

    @Id
    @Comment("配置ID")
    @GeneratedValue(generator = "generate_config_id")
    @SequenceGenerator(name = "generate_config_id", sequenceName = "seq_generate_config_id")
    private Long id;

    @Column(name = "code", columnDefinition = "varchar(100) COMMENT '配置代码'", nullable = false, unique = true)
    private String code;

    @Column(name = "title", columnDefinition = "varchar(200) COMMENT '配置标题'", nullable = false)
    private String title;

    @Type(value = FMTemplateUT.class, parameters = {@org.hibernate.annotations.Parameter(name = "basePath", value = "classpath:freemark_template/")})
    @Column(name = "system_template", columnDefinition = "longtext COMMENT '系统模板'")
    private FMTemplate systemTemplate;

    @Type(value = FMTemplateUT.class, parameters = {@org.hibernate.annotations.Parameter(name = "basePath", value = "classpath:freemark_template/")})
    @Column(name = "user_template", columnDefinition = "longtext COMMENT '用户模板'")
    private FMTemplate userTemplate;
//
//    @Type(FMTemplateUT.class)
//    @Column(name = "enhanced_system_template", columnDefinition = "longtext COMMENT '增强系统模板（支持include）'")
//    private FMTemplate enhancedSystemTemplate;
//
//    @Type(FMTemplateUT.class)
//    @Column(name = "enhanced_user_template", columnDefinition = "longtext COMMENT '增强用户模板（支持include）'")
//    private FMTemplate enhancedUserTemplate;

    @Enumerated(EnumType.STRING)
    @Comment("生成器类型")
    @Column(name = "generate_type", nullable = false)
    private GenerateDTO.Type generateType;

    @Column(name = "result_type", columnDefinition = "varchar(255) COMMENT '状态'")
    private String resultType;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "model_id", columnDefinition = "bigint(20) COMMENT '模型ID'")
    private Model<?> model;

    @Type(MapContentUT.class)
    @Column(name = "properties", columnDefinition = "varchar(511) COMMENT '流程属性配置'")
    private MapContent properties = new MapContent();

    public Generate newGenerate(Flow flow, FlowNode flowNode) {
        return Generate.newGenerate(this, flow, flowNode);
    }

}
