package com.dcai.aixg.domain.aiagent.flow;

import com.dcai.aixg.domain.aiagent.commons.ProcessBase;
import com.dcai.aixg.domain.aiagent.generate.GenerateConfig;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@Entity
@NoArgsConstructor
@DiscriminatorValue("GENERATE")
@SubtypeCode(parent = FlowNode.class, code = "GENERATE", name = "生成器")
public class FlowNode4Generate extends FlowNode {

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "generate_config_id", columnDefinition = "bigint(20) COMMENT '生成器配置ID'")
    private GenerateConfig generateConfig;

    @Override
    public ProcessBase<?> newProcess(Flow flow) {
        return generateConfig.newGenerate(flow, this);
    }
}
