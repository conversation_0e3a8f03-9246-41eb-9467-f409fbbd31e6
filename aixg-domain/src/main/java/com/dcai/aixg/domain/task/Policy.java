package com.dcai.aixg.domain.task;

import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Entity
@NoArgsConstructor
@Comment("政策")
@Table(name = "tb_policy")
@Where(clause = "logic_delete = 0")
public class Policy {

    @Id
    @Comment("政策id")
    @GeneratedValue(generator = "policy_id")
    @SequenceGenerator(name = "policy_id", sequenceName = "seq_policy_id")
    private Long id;

    @Column(name = "cityName", columnDefinition = "varchar(255) COMMENT '城市名称'")
    private String cityName;

    @Column(name = "title", columnDefinition = "varchar(255) COMMENT '政策标题'")
    private String title;

}
