package com.dcai.aixg.dto.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 图表数据系列
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "图表数据系列")
public class ChartSeries {
    
    /**
     * 系列名称
     */
    @Schema(description = "系列名称", example = "销售额")
    private String title;
    
    /**
     * 数据值（柱状图/折线图为数值数组，饼图为单个数值）
     */
    @Schema(description = "数据值")
    private Object content;
    

}
