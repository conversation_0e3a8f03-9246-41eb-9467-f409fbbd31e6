package com.dcai.aixg.dto.ai;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 文档控件基类
 * 
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "文档控件基类")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "type"
)
@JsonSubTypes({
    @JsonSubTypes.Type(value = TextWidget.class, name = "TEXT"),
    @JsonSubTypes.Type(value = ImageWidget.class, name = "IMAGE"),
    @JsonSubTypes.Type(value = ListWidget.class, name = "LIST"),
    @JsonSubTypes.Type(value = TableWidget.class, name = "TABLE"),
    @JsonSubTypes.Type(value = ChartWidget.class, name = "CHART"),
    @JsonSubTypes.Type(value = HousingCardWidget.class, name = "HOUSING_CARD")
})
public abstract class DocumentWidget {
    
    /**
     * 控件编号，用于定位控件在文档中的位置
     *
     * 编号规则：对完整态文档各要素进行分级编号(章节->段落->栏目->其他控件)
     * 文档可以根据需求灵活选择层级数量，不必使用完整的四级结构
     *
     * 层级示例：
     * - 简单文档：1, 2, 3 (仅章节级别)
     * - 两级文档：1, 1.1, 1.2, 2, 2.1 (章节+段落)
     * - 三级文档：1, 1.1, 1.1.1, 1.1.2, 1.2 (章节+段落+栏目)
     * - 完整文档：1, 1.1, 1.1.1, *******, ******* (四级完整结构)
     *
     * 层级映射：
     * - TEXT + SECTION → 章节级别
     * - TEXT + PARAGRAPH → 段落级别
     * - TEXT + ENTRY → 栏目级别
     * - 其他所有控件 → 控件级别
     */
    @Schema(description = "控件编号，分级编号(章节->段落->栏目->控件)，可灵活选择层级数量",
            example = "1.2.3",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private String serial;

    /**
     * 控件样式
     */
    @Schema(description = "控件样式")
    private WidgetStyle style;
    
    /**
     * 控件标题
     */
    @Schema(description = "控件标题", example = "章节标题")
    private String title;

    /**
     * 获取控件类型
     * 子类应该重写此方法返回对应的 WidgetType 枚举值
     *
     * 注意：此方法仅用于代码中获取类型信息，不会序列化到JSON中
     *
     * @return 控件类型枚举
     */
    @JsonIgnore
    public abstract WidgetType getWidgetType();

    /**
     * 控件样式枚举
     *
     * <AUTHOR>
     */
    @Getter
    public enum WidgetStyle {

        // 文本样式
        /**
         * 章节标题
         */
        SECTION("章节标题"),

        /**
         * 段落标题
         */
        PARAGRAPH("段落标题"),

        /**
         * 条目标题
         */
        ENTRY("条目标题"),

        /**
         * 无修饰
         */
        PLAIN("无修饰"),

        /**
         * 文本框
         */
        BLOCK("文本框"),

        /**
         * 重点突出
         */
        EMPHASIS("重点突出"),

        // 图片样式
        /**
         * 图片URL
         */
        URL("图片URL"),

        /**
         * SVG图片代码
         */
        SVG("SVG图片代码"),

        // 列表样式
        /**
         * 序号列表
         */
        SERIAL("序号列表"),

        /**
         * 无序列表
         */
        ITEM("无序列表"),

        // 表格样式
        /**
         * 普通表格
         */
        NORMAL("普通表格"),

        /**
         * 数据面板
         */
        BOARD("数据面板"),

        // 图表样式
        /**
         * 柱状图
         */
        BAR("柱状图"),

        /**
         * 折线图
         */
        LINE("折线图"),

        /**
         * 饼图
         */
        PIE("饼图");

        private final String description;

        WidgetStyle(String description) {
            this.description = description;
        }
    }

    /**
     * 控件类型枚举
     *
     * 注意：此枚举仅用于文档说明和代码可读性，实际的类型识别由 Jackson 的 @JsonTypeInfo 自动管理。
     * 控件实例不需要手动设置 type 字段，Jackson 会根据具体的控件类自动添加相应的 type 标识。
     *
     * <AUTHOR>
     */
    @Getter
    public enum WidgetType {

        /**
         * 文本控件
         */
        TEXT("文本"),

        /**
         * 图片控件
         */
        IMAGE("图片"),

        /**
         * 列表控件
         */
        LIST("列表"),

        /**
         * 表格控件
         */
        TABLE("表格"),

        /**
         * 图表控件
         */
        CHART("图表"),

        /**
         * 房产卡片控件
         */
        HOUSING_CARD("房产卡片");

        private final String description;

        WidgetType(String description) {
            this.description = description;
        }
    }
}
